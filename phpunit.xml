<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         processIsolation="false"
         stopOnFailure="false"
         executionOrder="random"
         failOnWarning="false"
         failOnRisky="false"
         failOnEmptyTestSuite="true"
         beStrictAboutOutputDuringTests="false"
         cacheDirectory=".phpunit.cache"
         backupGlobals="false">

    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory>tests/Integration</directory>
        </testsuite>
        <testsuite name="Api">
            <directory>tests/Api</directory>
        </testsuite>
        <testsuite name="Performance">
            <directory>tests/Performance</directory>
        </testsuite>
        <testsuite name="Browser">
            <directory>tests/Browser</directory>
        </testsuite>
    </testsuites>

    <source>
        <include>
            <directory>app</directory>
        </include>
        <exclude>
            <directory>app/Console</directory>
            <directory>app/Exceptions</directory>
            <directory>app/Http/Middleware</directory>
            <file>app/Http/Kernel.php</file>
        </exclude>
    </source>

    <coverage>
        <report>
            <html outputDirectory="tests/coverage/html"/>
            <clover outputFile="tests/coverage/clover.xml"/>
            <text outputFile="tests/coverage/coverage.txt"/>
        </report>
    </coverage>

    <logging>
        <junit outputFile="tests/results/junit.xml"/>
        <teamcity outputFile="tests/results/teamcity.txt"/>
    </logging>

    <php>
        <!-- Application Environment -->
        <env name="APP_ENV" value="testing"/>
        <env name="APP_KEY" value="base64:2fl+Ktvkdg+Fuz4Qp/A75G2RTiWVA/ZoKZvp6fiiM10="/>
        <env name="APP_DEBUG" value="true"/>
        <env name="APP_MAINTENANCE_DRIVER" value="file"/>

        <!-- Database Configuration -->
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>

        <!-- Cache Configuration -->
        <env name="CACHE_STORE" value="array"/>
        <env name="REDIS_HOST" value="127.0.0.1"/>
        <env name="REDIS_PASSWORD" value="null"/>
        <env name="REDIS_PORT" value="6379"/>
        <env name="REDIS_CLIENT" value="predis"/>

        <!-- Session Configuration -->
        <env name="SESSION_DRIVER" value="array"/>
        <env name="SESSION_LIFETIME" value="120"/>

        <!-- Queue Configuration -->
        <env name="QUEUE_CONNECTION" value="sync"/>

        <!-- Mail Configuration -->
        <env name="MAIL_MAILER" value="array"/>

        <!-- Security Configuration -->
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="HASH_DRIVER" value="bcrypt"/>

        <!-- API Configuration -->
        <env name="API_RATE_LIMITING_ENABLED" value="false"/>
        <env name="API_SECURITY_HEADERS" value="false"/>
        <env name="API_CORS_ENABLED" value="true"/>
        <env name="API_VALIDATION_CACHE" value="false"/>

        <!-- External Services -->
        <env name="PULSE_ENABLED" value="false"/>
        <env name="TELESCOPE_ENABLED" value="false"/>

        <!-- Testing Specific -->
        <env name="TESTING_PARALLEL" value="false"/>
        <env name="TESTING_COVERAGE_ENABLED" value="true"/>
        <env name="TESTING_MOCK_EXTERNAL_SERVICES" value="true"/>
    </php>
</phpunit>
