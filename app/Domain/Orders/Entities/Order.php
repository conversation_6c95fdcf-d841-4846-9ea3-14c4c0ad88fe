<?php

namespace App\Domain\Orders\Entities;

use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Orders\ValueObjects\Address;
use App\Domain\Orders\Events\OrderCreated;
use App\Domain\Orders\Events\OrderStatusChanged;
use App\Domain\Orders\Collections\OrderItemCollection;
use App\Domain\Shared\Traits\AggregateRoot;
use App\Domain\Shared\Contracts\EntityInterface;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use Carbon\Carbon;

class Order implements EntityInterface
{
    use AggregateRoot;

    private ?int $id;
    private int $userId;
    private OrderNumber $orderNumber;
    private Money $totalAmount;
    private Money $shippingCost;
    private Money $taxAmount;
    private Money $discountAmount;
    private OrderStatus $status;
    private PaymentStatus $paymentStatus;
    private string $paymentMethod;
    private string $shippingMethod;
    private ?Address $billingAddress;
    private ?Address $shippingAddress;
    private array $items = [];
    private array $notes = [];
    private ?string $couponCode;
    private ?string $trackingNumber;
    private ?string $shippingCompany;
    private ?Carbon $shippingDate;
    private ?Carbon $estimatedDeliveryDate;
    private ?Carbon $actualDeliveryDate;
    private ?string $invoiceNumber;
    private ?Carbon $invoiceDate;
    private ?string $adminNotes;
    private $customer;
    private Carbon $createdAt;
    private Carbon $updatedAt;

    public function __construct(
        int $userId,
        OrderNumber $orderNumber,
        Money $totalAmount,
        string $paymentMethod,
        string $shippingMethod,
        ?Address $billingAddress = null,
        ?Address $shippingAddress = null
    ) {
        $this->id = null;
        $this->userId = $userId;
        $this->orderNumber = $orderNumber;
        $this->totalAmount = $totalAmount;
        $this->paymentMethod = $paymentMethod;
        $this->shippingMethod = $shippingMethod;
        $this->billingAddress = $billingAddress;
        $this->shippingAddress = $shippingAddress;
        $this->status = OrderStatus::PENDING;
        $this->paymentStatus = PaymentStatus::PENDING;
        $this->shippingCost = Money::zero($totalAmount->getCurrency());
        $this->taxAmount = Money::zero($totalAmount->getCurrency());
        $this->discountAmount = Money::zero($totalAmount->getCurrency());
        $this->items = [];
        $this->notes = [];
        $this->couponCode = null;
        $this->trackingNumber = null;
        $this->shippingCompany = null;
        $this->shippingDate = null;
        $this->estimatedDeliveryDate = null;
        $this->actualDeliveryDate = null;
        $this->invoiceNumber = null;
        $this->invoiceDate = null;
        $this->adminNotes = null;
        $this->customer = null;
        $this->createdAt = Carbon::now();
        $this->updatedAt = Carbon::now();

        $this->recordEvent(new OrderCreated($this));
    }

    public static function create(
        int $userId,
        OrderNumber $orderNumber,
        Money $totalAmount,
        string $paymentMethod,
        string $shippingMethod,
        ?Address $billingAddress = null,
        ?Address $shippingAddress = null
    ): self {
        return new self(
            $userId,
            $orderNumber,
            $totalAmount,
            $paymentMethod,
            $shippingMethod,
            $billingAddress,
            $shippingAddress
        );
    }

    public function changeStatus(OrderStatus $newStatus, ?string $note = null): void
    {
        $oldStatus = $this->status;

        if (!$this->canChangeStatusTo($newStatus)) {
            throw new \InvalidArgumentException(
                "Cannot change order status from {$oldStatus->value} to {$newStatus->value}"
            );
        }

        $this->status = $newStatus;
        $this->updatedAt = Carbon::now();

        if ($note) {
            $this->addNote($note, 'status_change');
        }

        $this->recordEvent(new OrderStatusChanged($this, $oldStatus, $newStatus));
    }

    public function addItem(OrderItem $item): void
    {
        $this->items[] = $item;
        $this->recalculateTotal();
    }

    public function removeItem(int $itemId): void
    {
        $this->items = array_filter($this->items, fn($item) => $item->getId() !== $itemId);
        $this->recalculateTotal();
    }

    public function clearItems(): void
    {
        $this->items = [];
        $this->recalculateTotal();
    }

    public function addNote(string $note, string $type = 'general', bool $isPrivate = true): void
    {
        $this->notes[] = new OrderNote(
            note: $note,
            type: $type,
            isPrivate: $isPrivate,
            createdAt: Carbon::now()
        );
    }

    public function addExistingNote(OrderNote $note): void
    {
        $this->notes[] = $note;
    }

    public function setShippingInfo(string $trackingNumber, string $shippingCompany, ?Carbon $shippingDate = null): void
    {
        $this->trackingNumber = $trackingNumber;
        $this->shippingCompany = $shippingCompany;
        $this->shippingDate = $shippingDate ?? Carbon::now();
        $this->updatedAt = Carbon::now();

        if ($this->status === OrderStatus::READY_TO_SHIP) {
            $this->changeStatus(OrderStatus::SHIPPED);
        }
    }

    public function markAsDelivered(?Carbon $deliveryDate = null): void
    {
        $this->actualDeliveryDate = $deliveryDate ?? Carbon::now();
        $this->changeStatus(OrderStatus::DELIVERED);
    }

    public function cancel(?string $reason = null): void
    {
        if (!$this->canBeCancelled()) {
            throw new \InvalidArgumentException('Order cannot be cancelled in current status');
        }

        $this->changeStatus(OrderStatus::CANCELLED, $reason);
    }

    private function canChangeStatusTo(OrderStatus $newStatus): bool
    {
        // Comprehensive transition rules
        $allowedTransitions = [
            'pending' => [
                OrderStatus::PROCESSING,
                OrderStatus::PAYMENT_CONFIRMED,
                OrderStatus::CANCELLED,
            ],
            'processing' => [
                OrderStatus::PAYMENT_CONFIRMED,
                OrderStatus::PREPARING,
                OrderStatus::SHIPPED,
                OrderStatus::CANCELLED,
            ],
            'payment_confirmed' => [
                OrderStatus::PREPARING,
                OrderStatus::READY_TO_SHIP,
                OrderStatus::CANCELLED,
            ],
            'preparing' => [
                OrderStatus::READY_TO_SHIP,
                OrderStatus::CANCELLED,
            ],
            'ready_to_ship' => [
                OrderStatus::SHIPPED,
                OrderStatus::CANCELLED,
            ],
            'shipped' => [
                OrderStatus::DELIVERED,
                OrderStatus::REFUNDED,
            ],
            'delivered' => [
                OrderStatus::REFUNDED,
            ],
            'cancelled' => [], // Final state
            'refunded' => [], // Final state
        ];

        return in_array($newStatus, $allowedTransitions[$this->status->value] ?? []);
    }

    private function canBeCancelled(): bool
    {
        return in_array($this->status, [
            OrderStatus::PENDING,
            OrderStatus::PROCESSING,
            OrderStatus::PAYMENT_CONFIRMED,
            OrderStatus::PREPARING,
            OrderStatus::READY_TO_SHIP
        ]);
    }

    private function recalculateTotal(): void
    {
        $itemsTotal = Money::zero($this->totalAmount->getCurrency());

        foreach ($this->items as $item) {
            $itemsTotal = $itemsTotal->add($item->getSubtotal());
        }

        $this->totalAmount = $itemsTotal
            ->add($this->shippingCost)
            ->add($this->taxAmount)
            ->subtract($this->discountAmount);
    }

    // Getters
    public function getId(): ?int { return $this->id ?? null; }
    public function getUserId(): int { return $this->userId; }
    public function getOrderNumber(): OrderNumber { return $this->orderNumber; }
    public function getTotalAmount(): Money { return $this->totalAmount; }
    public function getShippingCost(): Money { return $this->shippingCost; }
    public function getTaxAmount(): Money { return $this->taxAmount; }
    public function getDiscountAmount(): Money { return $this->discountAmount; }
    public function getStatus(): string { return $this->status->value; }
    public function getStatusEnum(): OrderStatus { return $this->status; }
    public function getPaymentStatus(): PaymentStatus { return $this->paymentStatus; }
    public function getPaymentMethod(): string { return $this->paymentMethod; }
    public function getShippingMethod(): string { return $this->shippingMethod; }
    public function getBillingAddress(): ?Address { return $this->billingAddress; }
    public function getShippingAddress(): ?Address { return $this->shippingAddress; }
    public function getItems(): OrderItemCollection {
        return new OrderItemCollection($this->items);
    }
    public function getNotes(): array { return $this->notes; }
    public function getCouponCode(): ?string { return $this->couponCode; }
    public function getTrackingNumber(): ?string { return $this->trackingNumber; }
    public function getShippingCompany(): ?string { return $this->shippingCompany; }
    public function getShippingDate(): ?Carbon { return $this->shippingDate; }
    public function getEstimatedDeliveryDate(): ?Carbon { return $this->estimatedDeliveryDate; }
    public function getActualDeliveryDate(): ?Carbon { return $this->actualDeliveryDate; }
    public function getInvoiceNumber(): ?string { return $this->invoiceNumber; }
    public function getInvoiceDate(): ?Carbon { return $this->invoiceDate; }
    public function getAdminNotes(): ?string { return $this->adminNotes; }
    public function getCreatedAt(): Carbon { return $this->createdAt; }
    public function getUpdatedAt(): Carbon { return $this->updatedAt; }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
    public function setShippingCost(Money $shippingCost): void
    {
        $this->shippingCost = $shippingCost;
        $this->recalculateTotal();
    }

    public function setTaxAmount(Money $taxAmount): void
    {
        $this->taxAmount = $taxAmount;
        $this->recalculateTotal();
    }

    public function setDiscountAmount(Money $discountAmount): void
    {
        $this->discountAmount = $discountAmount;
        $this->recalculateTotal();
    }

    public function setPaymentStatus(PaymentStatus $paymentStatus): void
    {
        $this->paymentStatus = $paymentStatus;
    }

    public function setStatus($status): void
    {
        if (is_string($status)) {
            $this->status = OrderStatus::from($status);
        } else {
            $this->status = $status;
        }
        $this->updatedAt = Carbon::now();
    }

    public function setCouponCode(?string $couponCode): void
    {
        $this->couponCode = $couponCode;
    }

    public function setEstimatedDeliveryDate(?Carbon $estimatedDeliveryDate): void
    {
        $this->estimatedDeliveryDate = $estimatedDeliveryDate;
    }

    public function setInvoiceNumber(?string $invoiceNumber): void
    {
        $this->invoiceNumber = $invoiceNumber;
    }

    public function setTotalAmount(Money $totalAmount): void
    {
        $this->totalAmount = $totalAmount;
        $this->updatedAt = Carbon::now();
    }

    public function setPaymentMethod(?string $paymentMethod): void
    {
        $this->paymentMethod = $paymentMethod ?? '';
        $this->updatedAt = Carbon::now();
    }

    public function setShippingAddress($shippingAddress): void
    {
        $this->shippingAddress = $shippingAddress;
        $this->updatedAt = Carbon::now();
    }

    public function setCreatedAt(Carbon $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function setDeliveredAt(?Carbon $deliveredAt): void
    {
        $this->actualDeliveryDate = $deliveredAt;
        $this->updatedAt = Carbon::now();
    }

    public function getDeliveredAt(): ?Carbon
    {
        return $this->actualDeliveryDate;
    }

    public function isPaid(): bool
    {
        return $this->paymentStatus === PaymentStatus::PAID;
    }

    public function setPaid(bool $paid): void
    {
        $this->paymentStatus = $paid ? PaymentStatus::PAID : PaymentStatus::PENDING;
        $this->updatedAt = Carbon::now();
    }

    public function markAsPaid(): void
    {
        $this->paymentStatus = PaymentStatus::PAID;
        $this->updatedAt = Carbon::now();
    }

    public function getCustomer()
    {
        return $this->customer;
    }

    public function setCustomer($customer): void
    {
        $this->customer = $customer;
    }

    public function setInvoiceDate(?Carbon $invoiceDate): void
    {
        $this->invoiceDate = $invoiceDate;
    }

    public function setAdminNotes(?string $adminNotes): void
    {
        $this->adminNotes = $adminNotes;
    }

    public function setActualDeliveryDate(?Carbon $actualDeliveryDate): void
    {
        $this->actualDeliveryDate = $actualDeliveryDate;
    }

    // EntityInterface implementation
    public function equals(EntityInterface $entity): bool
    {
        if (!$entity instanceof Order) {
            return false;
        }

        return $this->getId() === $entity->getId() &&
               $this->getOrderNumber()->getValue() === $entity->getOrderNumber()->getValue();
    }

    public function toString(): string
    {
        return sprintf(
            'Order[id=%s, orderNumber=%s, status=%s, total=%s]',
            $this->getId() ?? 'null',
            $this->getOrderNumber()->getValue(),
            $this->getStatus(),
            $this->getTotalAmount()->toString()
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->getId(),
            'user_id' => $this->getUserId(),
            'order_number' => $this->getOrderNumber()->getValue(),
            'total_amount' => $this->getTotalAmount()->getAmount(),
            'currency' => $this->getTotalAmount()->getCurrency(),
            'status' => $this->getStatus(),
            'payment_status' => $this->getPaymentStatus()->value,
            'payment_method' => $this->getPaymentMethod(),
            'shipping_method' => $this->getShippingMethod(),
            'created_at' => $this->getCreatedAt()->toISOString(),
            'updated_at' => $this->getUpdatedAt()->toISOString(),
        ];
    }
}
