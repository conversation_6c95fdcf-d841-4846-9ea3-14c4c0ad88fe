<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" tests="8" assertions="32" errors="0" failures="1" skipped="0" time="1.828268">
    <testcase name="test_allows_valid_transition_processing_to_shipped" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="61" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="4" time="0.624997"/>
    <testcase name="test_denies_transition_without_target_status" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="271" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="4" time="0.157764"/>
    <testcase name="test_denies_payment_confirmation_without_payment" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="161" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="4" time="0.154176"/>
    <testcase name="test_allows_valid_transition_pending_to_payment_confirmed" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="30" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="5" time="0.184691"/>
    <testcase name="test_denies_cancellation_from_delivered" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="147" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="4" time="0.189677"/>
    <testcase name="test_allows_cancellation_from_pending" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="104" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="4" time="0.167444"/>
    <testcase name="test_denies_refund_from_non_delivered" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="227" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="4" time="0.177404"/>
    <testcase name="test_requires_approval_for_specific_transitions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="297" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="3" time="0.172115">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_requires_approval_for_specific_transitions&#13;
Failed asserting that true is false.
&#13;
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:308</failure>
    </testcase>
  </testsuite>
</testsuites>
