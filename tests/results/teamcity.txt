##teamcity[testCount count='28' flowId='6520']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest' flowId='6520']
##teamcity[testStarted name='test_allows_valid_transition_processing_to_shipped' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_processing_to_shipped' flowId='6520']
##teamcity[testFinished name='test_allows_valid_transition_processing_to_shipped' duration='14' flowId='6520']
##teamcity[testStarted name='test_denies_transition_without_target_status' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_transition_without_target_status' flowId='6520']
##teamcity[testFinished name='test_denies_transition_without_target_status' duration='3' flowId='6520']
##teamcity[testStarted name='test_denies_payment_confirmation_without_payment' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_payment_confirmation_without_payment' flowId='6520']
##teamcity[testFinished name='test_denies_payment_confirmation_without_payment' duration='3' flowId='6520']
##teamcity[testStarted name='test_allows_valid_transition_pending_to_payment_confirmed' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_pending_to_payment_confirmed' flowId='6520']
##teamcity[testFinished name='test_allows_valid_transition_pending_to_payment_confirmed' duration='5' flowId='6520']
##teamcity[testStarted name='test_denies_cancellation_from_delivered' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_cancellation_from_delivered' flowId='6520']
##teamcity[testFinished name='test_denies_cancellation_from_delivered' duration='7' flowId='6520']
##teamcity[testStarted name='test_allows_cancellation_from_pending' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_cancellation_from_pending' flowId='6520']
##teamcity[testFinished name='test_allows_cancellation_from_pending' duration='4' flowId='6520']
##teamcity[testStarted name='test_denies_refund_from_non_delivered' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_refund_from_non_delivered' flowId='6520']
##teamcity[testFinished name='test_denies_refund_from_non_delivered' duration='4' flowId='6520']
##teamcity[testStarted name='test_requires_approval_for_specific_transitions' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_requires_approval_for_specific_transitions' flowId='6520']
##teamcity[testFailed name='test_requires_approval_for_specific_transitions' message='Failed asserting that true is false.' details='C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:308|n' duration='9' flowId='6520']
##teamcity[testFinished name='test_requires_approval_for_specific_transitions' duration='13' flowId='6520']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest' flowId='6520']
